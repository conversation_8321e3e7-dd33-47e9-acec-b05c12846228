# Configuration settings for the Cloud Function
import json
import sendgrid
from google.cloud import secretmanager

PDF_FOLDER_PREFIX = "notices"

PROJECT_ID="webnyay-ai"
name = f"projects/{PROJECT_ID}/secrets/bfsi/versions/latest"

# Instantiate Secret Manager Client
client = secretmanager.SecretManagerServiceClient()
  
# Get Response
response = client.access_secret_version(request={"name": name})

# Extract 'Plain Text' Key
secret_values = response.payload.data.decode("UTF-8")
# print(f'got secret')
#print(f'secret type is {type(secret_values)} and secret is {secret_values}')
secret_values = json.loads(secret_values, strict=False)

SENDGRID_API_KEY=secret_values['SENDGRID_API_KEY']
sg = sendgrid.SendGridAPIClient(api_key=SENDGRID_API_KEY)

MAIL_ID = "<EMAIL>"
