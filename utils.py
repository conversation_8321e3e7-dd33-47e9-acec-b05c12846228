from settings import MAIL_ID, sg
import sendgrid
from google.cloud import storage

def sendMail(to, subject, value=""):
    mail = sendgrid.helpers.mail.Mail(from_email=MAIL_ID, to_emails=to, subject=subject, html_content=value)
    response = sg.send(mail)
    # print(response.status_code)
    print(response.body)
    # print(response.headers)


# Initialize GCS client
storage_client = storage.Client()

class GCSManager:
    def __init__(self, bucket_name):
        self.bucket_name = bucket_name
        self.bucket = storage_client.bucket(bucket_name)

    def read_file(self, file_path):
        blob = self.bucket.blob(file_path)
        return blob.download_as_bytes()

    def upload_file(self, file_path, file_data):
        blob = self.bucket.blob(file_path)
        blob.upload_from_file(file_data)
