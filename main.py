import io
import os
import base64
import json
import pandas as pd
import functions_framework
from PyPDF2 import PdfReader, PdfWriter
from sqlalchemy import text
import sqlalchemy
from google.cloud.sql.connector import Connector
from utils import sendMail, GCSManager
from datetime import datetime
import threading
import time

GCS_BUCKET_NAME = os.environ.get('GCS_BUCKET_NAME')

pool = None

def connect_to_instance() -> sqlalchemy.engine.base.Engine:    
    connector = Connector(refresh_strategy="lazy")
    instance_conn_name = os.environ['INSTANCE_CONNECTION_NAME']
    db_name = os.environ.get('DB_NAME')
    db_user = os.environ.get('DB_USER')
    db_password = os.environ.get('DB_PASSWORD')
    def getconn():
        conn = connector.connect(
            instance_conn_name,
            'pg8000',
            user=db_user,
            password=db_password,
            db=db_name
        )
        print("----------------successfully connected to db---------------------")
        return conn

    return sqlalchemy.create_engine(
        "postgresql+pg8000://",
        creator      = getconn,
        pool_size    = 50,
        max_overflow = 10,
        # pool_timeout = 30,
        pool_recycle = 300
    )

def split_pdf(pdf_bytes, num_pages_per_pdf):
    pdf_reader = PdfReader(pdf_bytes)
    total_pages = len(pdf_reader.pages)
    split_pdfs = []

    for start_page in range(0, total_pages, num_pages_per_pdf):
        pdf_writer = PdfWriter()
        end_page = min(start_page + num_pages_per_pdf, total_pages)

        for page_num in range(start_page, end_page):
            pdf_writer.add_page(pdf_reader.pages[page_num])

        output_pdf = io.BytesIO()
        pdf_writer.write(output_pdf)
        output_pdf.seek(0)  # Make sure to reset the file pointer
        split_pdfs.append(output_pdf)

    return split_pdfs

def get_dispute_ids_by_loan_ids(pool, loan_ids, campaign_id):
    """Get dispute IDs for multiple loan_ids in a single query"""
    # Convert loan_ids to a comma-separated string with quotes
    loan_ids_str = "', '".join(loan_ids)
    loan_ids_str = f"('{loan_ids_str}')"

    query = text(f"""
        SELECT id, loan_id 
        FROM odr_dispute 
        WHERE loan_id IN {loan_ids_str} AND campaign_id = :campaign_id AND status != 'closed'
    """)

    try:
        with pool.connect() as connection:
            result = connection.execute(query, {"campaign_id": campaign_id})
            # Create a mapping of loan_id to dispute_id
            loan_to_dispute_map = {row[1]: row[0] for row in result}
            return loan_to_dispute_map
    except Exception as e:
        return {}


def filter_loans_preserve_excel_order(excel_loan_ids, dispute_map):
    """Filter loan IDs to only include those with disputes, preserving Excel order"""
    # Keep only loan_ids that exist in dispute_map, maintaining Excel order
    filtered_loan_ids = [loan_id for loan_id in excel_loan_ids if loan_id in dispute_map]
    return filtered_loan_ids


def batch_create_notices(pool, notices_data):
    """Create multiple Notice records in a single transaction"""
    if not notices_data:
        return 0

    # Prepare values part of the query
    values_parts = []
    params = {}

    for i, notice in enumerate(notices_data):
        value_part = f"(:file_path_{i}, :file_name_{i}, :notice_type_{i}, :created_by_id_{i}, :created_at_{i}, :dispute_id_{i}, :template_id_{i})"
        values_parts.append(value_part)

        params[f"file_path_{i}"] = notice['file_path']
        params[f"file_name_{i}"] = notice['file_name']
        params[f"notice_type_{i}"] = notice['notice_type']
        params[f"created_by_id_{i}"] = notice['created_by_id']
        params[f"created_at_{i}"] = notice['created_at']
        params[f"dispute_id_{i}"] = notice['dispute_id']
        params[f"template_id_{i}"] = notice['template_id']

    # Build the full query
    values_sql = ", ".join(values_parts)
    query = text(f"""
        INSERT INTO notice_notice 
        (file_path, file_name, notice_type, created_by_id, created_at, dispute_id, template_id)
        VALUES {values_sql}
    """)

    try:
        with pool.connect() as connection:
            connection.execute(query, params)
            connection.commit()
            return len(notices_data)
    except Exception as e:
        raise

def update_template_progress(pool, template_id, splits_processed, total_splits=None, status=None, error=None):
    """Update the template progress in the database"""
    # Build the update query based on what we need to update
    update_parts = ["splits_processed = :splits_processed"]
    params = {
        "template_id": template_id,
        "splits_processed": splits_processed
    }
    
    if total_splits is not None:
        update_parts.append("total_splits_expected = :total_splits")
        params["total_splits"] = total_splits
        
    if status is not None:
        update_parts.append("splitting_status = :status")
        params["status"] = status
        
    if error is not None:
        # Add the error to the existing errors array
        update_parts.append("splitting_errors = splitting_errors || :error")
        params["error"] = json.dumps([error])
    
    update_sql = ", ".join(update_parts)
    update_query = text(f"""
        UPDATE notice_template
        SET {update_sql}
        WHERE id = :template_id
    """)
    
    try:
        with pool.connect() as connection:
            connection.execute(update_query, params)
            connection.commit()
    except Exception as e:
        print(f"Database query failed to update template progress: {str(e)}")


@functions_framework.cloud_event
def split_pdf_function(cloud_event):
    """Cloud Function triggered by Pub/Sub event with extreme performance optimization."""
    start_time = datetime.now()
    
    try:
        # Get message data from the Pub/Sub event
        pubsub_message = base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
        message_data = json.loads(pubsub_message)

        # Extract parameters from the message
        pdf_file_name = message_data.get('pdf_file')
        excel_file_name = message_data.get('excel_file')
        num_pages_per_pdf = int(message_data.get('num_pages_per_pdf'))
        recipient_email = message_data.get('recipient_email')
        campaign_id = message_data.get('campaign_id')
        template_id = message_data.get('template_id')
        template_name = message_data.get('template_name')
        user_id = message_data.get('user_id')
        notice_type = message_data.get('notice_type', 'Default')

        # Get bucket name from settings
        bucket_name = GCS_BUCKET_NAME

        # Connect to the database immediately
        global pool
        if not pool:
            pool = connect_to_instance()
            
        # Initialize with status "processing"
        update_template_progress(pool, template_id, 0, status="processing")

        # Initialize GCS manager
        gcs_manager = GCSManager(bucket_name)
        
        # STEP 1: Download files in parallel using threads
        # print("Starting file downloads...")
        download_time_start = datetime.now()
        
        # Use threads to download files in parallel
        pdf_data = None
        excel_data = None
        
        def download_pdf():
            nonlocal pdf_data
            pdf_file_path = f'notices/{campaign_id}/{pdf_file_name}'
            pdf_data = gcs_manager.read_file(pdf_file_path)
            
        def download_excel():
            nonlocal excel_data
            excel_file_path = f'notices/{campaign_id}/{excel_file_name}'
            excel_data = gcs_manager.read_file(excel_file_path)
        
        # Create and start threads
        pdf_thread = threading.Thread(target=download_pdf)
        excel_thread = threading.Thread(target=download_excel)
        
        pdf_thread.start()
        excel_thread.start()
        
        # Wait for both downloads to complete
        pdf_thread.join()
        excel_thread.join()
        
        if not pdf_data or not excel_data:
            raise Exception("Failed to download one or both files")
        
        download_time_end = datetime.now()
        download_duration = (download_time_end - download_time_start).total_seconds()

        # STEP 2: Process the Excel file to get loan IDs
        excel_process_start = datetime.now()
        excel_bytes = io.BytesIO(excel_data)
        df = pd.read_excel(excel_bytes, engine='openpyxl')
        loan_ids_from_excel = [str(loan_id) for loan_id in df['Loan ID'].tolist()]
        # loan_ids = df['Loan ID'].tolist()

        #remove closed loans
        loan_to_dispute_map = get_dispute_ids_by_loan_ids(pool, loan_ids_from_excel, campaign_id)
        loan_ids = filter_loans_preserve_excel_order(loan_ids_from_excel, loan_to_dispute_map)
        print(f'loan_ids going to be processed are {loan_ids} and found {len(loan_to_dispute_map)} dispute IDs')

        total_loans = len(loan_ids)
        
        # STEP 3: Setup PDF processing
        folder_name = pdf_file_name.split('.')[0]
        folder_path = f'notices/{campaign_id}/{template_id}/{template_name}/'
        
        # Create a PDF reader
        pdf_reader_start = datetime.now()
        pdf_bytes = io.BytesIO(pdf_data)
        pdf_reader = PdfReader(pdf_bytes)
        total_pages = len(pdf_reader.pages)
        
        # Calculate the expected number of PDFs based on page count
        expected_pdfs = (total_pages + num_pages_per_pdf - 1) // num_pages_per_pdf
        
        # Check if we have enough loan IDs
        if expected_pdfs > total_loans:
            error_msg = f"Not enough loan IDs. Need {expected_pdfs} but only have {total_loans}"
            update_template_progress(pool, template_id, 0, status="failed", error=error_msg)
            raise Exception(error_msg)
        
        # Limit the loan IDs to process based on PDFs needed
        loan_ids_to_process = loan_ids[:expected_pdfs]
        
        # Update total expected splits to the correct number
        update_template_progress(pool, template_id, 0, total_splits=expected_pdfs)
        
        # STEP 4: Query dispute IDs in a single database call (only for the loan IDs we'll actually use)
        dispute_query_start = datetime.now()
        
        # STEP 5: Split and process PDFs with extreme optimization
        process_start = datetime.now()
        
        # Constants for optimization
        MAX_CONCURRENT_UPLOADS = 8  # Number of concurrent uploads
        BATCH_SIZE = 50  # Database batch size
        
        # Initialize tracking variables
        splits_processed = 0
        notices_created = 0
        notices_data = []
        upload_threads = []
        upload_semaphore = threading.Semaphore(MAX_CONCURRENT_UPLOADS)
        upload_lock = threading.Lock()
        now = datetime.now()
        last_update_time = now
        
        # Function for uploading split PDFs in parallel
        def upload_split_pdf(loan_id, pdf_writer):
            nonlocal splits_processed, notices_data, last_update_time, notices_created
            
            try:
                # Acquire semaphore to limit concurrent uploads
                upload_semaphore.acquire()
                
                # Process the split
                output_pdf = io.BytesIO()
                pdf_writer.write(output_pdf)
                output_pdf.seek(0)
                
                # Define file paths
                split_pdf_name = f'{loan_id}.pdf'
                split_pdf_path = f'{folder_path}{split_pdf_name}'
                
                # Upload to GCS
                gcs_manager.upload_file(split_pdf_path, output_pdf)
                output_pdf.close()
                
                # Check if we have a dispute ID for this loan ID
                with upload_lock:
                    if loan_id in loan_to_dispute_map:
                        dispute_id = loan_to_dispute_map[loan_id]
                        notices_data.append({
                            'file_path': split_pdf_path,
                            'file_name': split_pdf_name,
                            'notice_type': notice_type,
                            'created_by_id': user_id,
                            'created_at': now,
                            'dispute_id': dispute_id,
                            'template_id': template_id
                        })
                    
                    # Increment counter
                    splits_processed += 1
                    
                    # Update progress every 3 seconds or at the end
                    current_time = datetime.now()
                    if (current_time - last_update_time).total_seconds() >= 3 or splits_processed == expected_pdfs:
                        update_template_progress(pool, template_id, splits_processed)
                        last_update_time = current_time
                        elapsed = (current_time - process_start).total_seconds()
                        rate = splits_processed / elapsed if elapsed > 0 else 0
                        
                        # Insert notices in batches when we have enough
                        if len(notices_data) >= BATCH_SIZE:
                            try:
                                batch_data = notices_data.copy()
                                notices_data.clear()
                                batch_count = batch_create_notices(pool, batch_data)
                                notices_created += batch_count
                            except Exception as e:
                                error_msg = f"Error creating notices batch: {str(e)}"
                                update_template_progress(pool, template_id, splits_processed, error=error_msg)
            
            except Exception as e:
                with upload_lock:
                    error_msg = f"Error processing split for loan_id {loan_id}: {str(e)}"
                    update_template_progress(pool, template_id, splits_processed, error=error_msg)
            
            finally:
                # Release semaphore
                upload_semaphore.release()
        
        # Process PDF splits with high concurrency
        threads = []
        for i, loan_id in enumerate(loan_ids_to_process):
            # No need to check i >= total_loans since we're using the limited list
            try:
                # Calculate page range correctly
                start_page = i * num_pages_per_pdf
                end_page = min(start_page + num_pages_per_pdf, total_pages)
                # Create PDF writer
                pdf_writer = PdfWriter()
                for page_num in range(start_page, end_page):
                    pdf_writer.add_page(pdf_reader.pages[page_num])
                # Create thread for upload
                thread = threading.Thread(target=upload_split_pdf, args=(loan_id, pdf_writer))
                threads.append(thread)
                thread.start()
                # Limit active threads to prevent memory issues - Fixed infinite loop
                # Clean up completed threads first
                threads = [t for t in threads if t.is_alive()]
                # Wait for threads to complete if we have too many active
                while len(threads) >= MAX_CONCURRENT_UPLOADS:
                    time.sleep(0.1)
                    # Clean up completed threads during wait
                    threads = [t for t in threads if t.is_alive()]
            except Exception as e:
                error_msg = f"Error creating split for loan_id {loan_id}: {str(e)}"
                update_template_progress(pool, template_id, splits_processed, error=error_msg)
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
            
        # Insert any remaining notices
        if notices_data:
            try:
                batch_count = batch_create_notices(pool, notices_data)
                notices_created += batch_count
            except Exception as e:
                update_template_progress(pool, template_id, splits_processed, error=error_msg)

        process_end = datetime.now()
        process_duration = (process_end - process_start).total_seconds()

        # Clean up resources
        pdf_bytes.close()
        excel_bytes.close()
        
        # STEP 6: Finalize processing
        print(f"Successfully processed and split PDF into {splits_processed} files")

        # Update the template to mark renaming and splitting as done
        update_template_table(pool, template_id)
        
        # Update final status
        update_template_progress(pool, template_id, splits_processed, status="completed")

        # Send email notification
        sendMail(recipient_email, f"PDF Split Complete - {pdf_file_name}", 
                f"Your PDF has been successfully split into {splits_processed} files and {notices_created} notice records were created.")

        end_time = datetime.now()
        total_duration = (end_time - start_time).total_seconds()
        
        return "Success"
    except Exception as e:
        error_msg = f"Error processing the message: {str(e)}"
        
        # If we have a connection and template_id, mark as failed
        if pool and 'template_id' in locals():
            update_template_progress(pool, template_id, 
                                    splits_processed if 'splits_processed' in locals() else 0, 
                                    status="failed", 
                                    error=error_msg)
        
        end_time = datetime.now()
        total_duration = (end_time - start_time).total_seconds()
        
        raise e


def update_template_table(pool, template_id):
    update_query = text("""
        UPDATE notice_template
        SET renaming_and_splitting_done = true
        WHERE id = :template_id
    """)
    try:
        with pool.connect() as connection:
            connection.execute(update_query, {
                "template_id": template_id
            })
            connection.commit()
            # print(f"Updated template {template_id} - marked renaming_and_splitting_done as true")
    except Exception as e:
        raise

